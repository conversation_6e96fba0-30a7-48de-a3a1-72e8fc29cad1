{
  "name": "libra-cdn",
  "main": "src/index.ts",
  "compatibility_date": "2025-07-23",
  "compatibility_flags": [
    "nodejs_compat",
    "global_fetch_strictly_public"
  ],
  "assets": {
    "binding": "ASSETS",
    "directory": "public"
  },
  "routes": [
    {
      "pattern": "cdn.libra.dev",
      "custom_domain": true
    }
  ],
  "hyperdrive": [
    {
      "binding": "HYPERDRIVE",
      "id": "{{HYPERDRIVE_ID}}",
    }
  ],
  "d1_databases": [
    {
      "binding": "DATABASE",
      "database_name": "libra",
      "database_id": "{{DATABASE_ID}}"
    }
  ],
  "kv_namespaces": [
    {
      "binding": "KV",
      "id": "{{KV_NAMESPACE_ID}}"
    }
  ],
  "r2_buckets": [
    {
      "binding": "BUCKET",
      "bucket_name": "libra-cdn"
    }
  ],
  "images": {
    "binding": "IMAGES"
  },
  "unsafe": {
    "bindings": [
      {
        "name": "FILE_UPLOAD_RATE_LIMITER",
        "type": "ratelimit",
        "namespace_id": "1001",
        "simple": {
          "limit": 1,
          "period": 10
        }
      }
    ]
  },
  "observability": {
    "enabled": true,
    "head_sampling_rate": 1
  },
  "vars": {
    // Environment configuration
    "ENVIRONMENT": "{{ENVIRONMENT}}",

    // Core application configuration
    "NEXT_PUBLIC_APP_URL": "{{NEXT_PUBLIC_APP_URL}}",
    "NEXT_PUBLIC_CDN_URL": "{{NEXT_PUBLIC_CDN_URL}}",

    // Authentication configuration
    "BETTER_AUTH_SECRET": "{{BETTER_AUTH_SECRET}}",
    "BETTER_GITHUB_CLIENT_ID": "{{BETTER_GITHUB_CLIENT_ID}}",
    "BETTER_GITHUB_CLIENT_SECRET": "{{BETTER_GITHUB_CLIENT_SECRET}}",

    // Security configuration
    "TURNSTILE_SECRET_KEY": "{{TURNSTILE_SECRET_KEY}}",

    // Payment configuration (optional)
    "STRIPE_SECRET_KEY": "{{STRIPE_SECRET_KEY}}",
    "STRIPE_WEBHOOK_SECRET": "{{STRIPE_WEBHOOK_SECRET}}",

    // Admin configuration (optional)
    "ADMIN_USER_IDS": "{{ADMIN_USER_IDS}}",

    // Database configuration
    "POSTGRES_URL": "{{POSTGRES_URL}}",
    "DATABASE_ID": "{{DATABASE_ID}}",

    // Cloudflare configuration
    "CLOUDFLARE_ACCOUNT_ID": "{{CLOUDFLARE_ACCOUNT_ID}}",
    "CLOUDFLARE_API_TOKEN": "{{CLOUDFLARE_API_TOKEN}}",
    "CLOUDFLARE_ZONE_ID": "{{CLOUDFLARE_ZONE_ID}}",
    "CLOUDFLARE_AIGATEWAY_NAME": "{{CLOUDFLARE_AIGATEWAY_NAME}}",

    // Email service (optional)
    "RESEND_API_KEY": "{{RESEND_API_KEY}}",
    "RESEND_FROM": "{{RESEND_FROM}}",

    "LOG_LEVEL": "{{LOG_LEVEL}}",
  }
}
